<!-- 解析ViewData["VisualPageData"]中的header配置 -->
{% assign visualPageData = ViewData["VisualPageData"] | jsonparse %}
{% assign headerConfig = null %}
{% assign showHeader = true %}
{% assign showSearch = true %}
{% assign showUser = true %}
{% assign showShoppingCart = true %}
{% assign showLanguageSwitch = true %}
{% assign showMenu = true %}
{% assign logoConfig = null %}
{% assign searchPlaceholder = null %}

{% comment %} 从Plugins列表中筛选header类型的插件 {% endcomment %}
{% assign headerPlugin = null %}
{% for plugin in visualPageData.Plugins %}
    {% assign cleanType = plugin.Type | strip %}
    {% if cleanType == "header" %}
        {% assign headerPlugin = plugin %}
        {% break %}
    {% endif %}
{% endfor %}
{% if headerPlugin %}
    {% assign displayValue = headerPlugin.Config.Display | append: "" %}
    {% if displayValue == "0" %}
        {% assign showHeader = false %}
    {% endif %}

    {% if headerPlugin.Settings %}
        {% assign searchValue = headerPlugin.Settings.Search | append: "" %}
        {% if searchValue == "0" %}
            {% assign showSearch = false %}
        {% endif %}
        {% assign userValue = headerPlugin.Settings.User | append: "" %}
        {% if userValue == "0" %}
            {% assign showUser = false %}
        {% endif %}
        {% assign shoppingCartValue = headerPlugin.Settings.ShoppingCart | append: "" %}
        {% if shoppingCartValue == "0" %}
            {% assign showShoppingCart = false %}
        {% endif %}
        {% assign languageSwitchValue = headerPlugin.Settings.LanguageSwitch | append: "" %}
        {% if languageSwitchValue == "0" %}
            {% assign showLanguageSwitch = false %}
        {% endif %}
        {% if headerPlugin.Settings.SearchPlaceholder %}
            {% assign searchPlaceholder = headerPlugin.Settings.SearchPlaceholder %}
        {% endif %}
    {% endif %}

    {% if headerPlugin.Blocks %}
        {% if headerPlugin.Blocks.Logo %}
            {% assign logoConfig = headerPlugin.Blocks.Logo %}
        {% endif %}
        {% if headerPlugin.Blocks.Menu %}
            {% assign menuValue = headerPlugin.Blocks.Menu.Menu | append: "" %}
            {% if menuValue == "0" %}
                {% assign showMenu = false %}
            {% endif %}
        {% endif %}
    {% endif %}
{% endif %}

{% if showHeader %}
<!--Topbar-->
<div class="top-info-bar style1 d-none d-lg-block d-md-block">
    <div class="topBar-slider-style1 alert fade show" role="alert">
        <div class="item text-uppercase text-center d-flex align-items-center justify-content-center">
            <a href="#">Purchase This Incredible Template Now! Sale end In</a>
            <!--Countdown Timer-->
            <div class="promo-counter days-time" data-countdown="2024/10/01"></div>
            <!--End Countdown Timer-->
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"><i class="an an-times-l"></i></button>
        </div>
        <div class="item text-center d-flex align-items-center justify-content-center">
            Up to <b class="mx-2" style="color:#2170b1;">80% Off</b> on entire store! hurry up! <a href="shop-left-sidebar.html" class="btn btn-small mx-2">Shop Now</a> <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"><i class="an an-times-l"></i></button>
        </div>
    </div>
</div>
<!--End Topbar-->
<!--Header wrap-->
<div class="header-main header-retevis-1">
    <!--Header-->
    <header id="header" class="header header-wrap mih-90 d-flex align-items-center">
        <div class="container-fluid">
            <div class="row">
                <!--Logo / Menu Toggle-->
                <div class="col-6 col-sm-6 col-md-6 col-lg-3 align-self-center justify-content-start d-flex">
                    <!--Mobile Toggle-->
                    <button type="button" class="btn--link site-header__menu js-mobile-nav-toggle mobile-nav--open me-3 d-lg-none"><i class="icon an an-times-l"></i><i class="icon an an-bars-l"></i></button>
                    <!--End Mobile Toggle-->
                    <!--Logo-->
                    <div class="logo">
                        <a href="/">
                            {% if logoConfig and logoConfig.Logo %}
                                <img class="logo-img mh-100"
                                     src="{{ logoConfig.Logo }}"
                                     alt="{% if logoConfig.ImageAlt %}{{ logoConfig.ImageAlt }}{% else %}Logo{% endif %}"
                                     title="{% if logoConfig.ImageAlt %}{{ logoConfig.ImageAlt }}{% else %}Logo{% endif %}"
                                     {% if logoConfig.LogoWidth %}width="{{ logoConfig.LogoWidth | replace: 'px', '' }}"{% else %}width="150"{% endif %}
                                     {% if logoConfig.LogoWidth or logoConfig.MobileLogoWidth %}
                                     style="{% if logoConfig.LogoWidth %}width: {{ logoConfig.LogoWidth }};{% endif %}{% if logoConfig.MobileLogoWidth %} --mobile-logo-width: {{ logoConfig.MobileLogoWidth }};{% endif %}"
                                     {% endif %}/>
                            {% else %}
                                <img class="logo-img mh-100" src="{{static_path}}/assets/images/logo-sticky-demo3.png" alt="Optimal Multipurpose eCommerce Bootstrap 5 Html Template" title="Optimal Multipurpose eCommerce Bootstrap 5 Html Template" width="150" />
                            {% endif %}
                            <span class="logo-txt d-none">Optimal</span>
                        </a>
                    </div>
                    <!--End Logo-->
                </div>
                <!--End Logo / Menu Toggle-->
                <!--Search Inline-->
                {% if showSearch %}
                <div class="col-1 col-sm-1 col-md-1 col-lg-6 d-none d-lg-block">
                    <form class="form minisearch search-inline" id="header-search1" action="#" method="get">
                        <label class="label d-none"><span>{{ "blog.global.searchBtn"|translate}}</span></label>
                        <div class="control">
                            <div class="searchField d-flex">
                                <div class="input-box d-flex w-100">
                                    <input type="text" name="q" value="" placeholder="{% if searchPlaceholder %}{{ searchPlaceholder }}{% else %}Search by keyword or #{% endif %}" class="input-text rounded-0 border-end-0">
                                    <button type="submit" title="Search" class="action search"><i class="icon an an-search-l"></i></button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                {% endif %}
                <!--End Search Inline-->
                <!--Right Action-->
                <div class="col-6 col-sm-6 col-md-6 col-lg-3 align-self-center icons-col text-right d-flex justify-content-end">
                    <!--Search-->
                    {% if showSearch %}
                    <div class="site-search iconset d-lg-none"><i class="icon an an-search-l"></i><span class="tooltip-label">Search</span></div>
                    {% endif %}
                    <!--End Search-->
                    <!--Setting Dropdown-->
                    {% if showUser %}
                    <div class="user-link iconset"><i class="icon an an-user-expand"></i><span class="tooltip-label">Account</span></div>
                    <div id="userLinks">
                        <ul class="user-links">
                            {% if IsLogined =="true" %}
                            <li><a class="dropdown-item" href="/Account/MyProfile">{{ "user.account.indexTitle"|translate}}</a></li>
                            <li><a class="dropdown-item" href="/Account/MyOrders">{{ "user.account.orderTitle"|translate}}</a></li>
                            <li><a class="dropdown-item" href="/Account/MyInbox">{{ "user.account.inboxTitle"|translate}}</a></li>
                            <li><a class="dropdown-item" href="/Account/MyAddress">{{ "user.account.addressTitle"|translate}}</a></li>
                            <li><a class="dropdown-item" href="/Account/MyCoupon">{{ "user.account.couponTitle"|translate}}</a></li>
                            <li><a class="dropdown-item" href="/Account/MyWishList">{{ "user.account.favoriteTitle"|translate}}</a></li>
                            <li><a class="dropdown-item" href="/Account/MyReview">{{ "user.account.reviewTitle"|translate}}</a></li>
                            <li><a class="dropdown-item" hx-post="/Account/Logout">{{ "user.account.logOut"|translate}}</a></li>
                            <li><!--<a class="dropdown-item" href="/" hx-post="/Account/Logout" hx-trigger="load">Log Out</a>--></li>
                            {% else %}
                            <li class="fw-bold"><a href="/Account/Login" class="header-username" da-event-click="">{{ "user.global.sign_in"|translate}}</a></li>
                            <li class="fw-bold"><a href="/user/SignUp" class="header-username" da-event-click="">{{ "user.register.register_title"|translate}}</a></li>

                            {% endif %}
                        </ul>
                    </div>
                    {% endif %}
                    <!--End Setting Dropdown-->
                    <!--Minicart Drawer-->
                    {% if showShoppingCart %}
                        {% if IsLogined =="true" %}
                        <div class="header-cart iconset">
                            <a href="/cart" class="site-header__cart btn-minicart">
                                <i class="icon an an-sq-bag"></i><span class="site-cart-count counter d-flex-center justify-content-center position-absolute translate-middle rounded-circle">0</span><span class="tooltip-label">Cart</span>
                            </a>
                        </div>
                        {% else %}
                        <div class="header-cart iconset">
                            <a href="cart-style1.html" class="site-header__cart btn-minicart" data-bs-toggle="modal" data-bs-target="#minicart-drawer">
                                <i class="icon an an-sq-bag"></i><span class="site-cart-count counter d-flex-center justify-content-center position-absolute translate-middle rounded-circle">0</span><span class="tooltip-label">Cart</span>
                            </a>
                        </div>
                        {% endif %}
                    {% endif %}
                    <!--End Minicart Drawer-->
                    <!--Wishlist-->
                    <div class="wishlist-link iconset">
                        <a href="/Account/MyWishList"><i class="icon an an-heart-l"></i><span class="wishlist-count counter d-flex-center justify-content-center position-absolute translate-middle rounded-circle">0</span><span class="tooltip-label">Wishlist</span></a>
                    </div>
                    <!--End Wishlist-->
                    <!--Setting Dropdown-->
                    {% if showLanguageSwitch %}
                    <div class="setting-link iconset pe-0"><i class="icon an an-globe"></i><span class="tooltip-label">Settings</span></div>
                    <div id="settingsBox">
                        <div class="currency-picker">
                            <span class="ttl">Select Currency</span>
                            <ul id="currencies" class="cnrLangList">
                                <li class="selected"><a href="#;" class="active">INR</a></li>
                                <li><a href="#;">GBP</a></li>
                                <li><a href="#;">CAD</a></li>
                                <li><a href="#;">USD</a></li>
                                <li><a href="#;">AUD</a></li>
                                <li><a href="#;">EUR</a></li>
                                <li><a href="#;">JPY</a></li>
                            </ul>
                        </div>
                        <div class="language-picker">
                            <span class="ttl">SELECT LANGUAGE</span>
                            <ul id="language" class="cnrLangList">
                                <li><a href="#" class="active">English</a></li>
                                <li><a href="#">French</a></li>
                                <li><a href="#">German</a></li>
                            </ul>
                        </div>
                    </div>
                    {% endif %}
                    <!--End Setting Dropdown-->
                </div>
                <!--End Right Action-->
            </div>
        </div>
        <!--Search Popup-->
        {% if showSearch %}
        <div id="search-popup" class="search-drawer">
            <div class="container">
                <span class="closeSearch an an-times-l"></span>
                <form class="form minisearch" id="header-search" action="#" method="get">
                    <label class="label"><span>{{ "blog.global.searchBtn"|translate}}</span></label>
                    <div class="control">
                        <div class="searchField">
                            <div class="search-category">
                                <select id="rgsearch-category" name="rgsearch[category]" data-default="All Categories">
                                    <option value="00" label="All Categories" selected="selected">All Categories</option>
                                    <optgroup id="rgsearch-shop" label="Shop">
                                        <option value="0">- All</option>
                                        <option value="1">- Men</option>
                                        <option value="2">- Women</option>
                                        <option value="3">- Shoes</option>
                                        <option value="4">- Blouses</option>
                                        <option value="5">- Pullovers</option>
                                        <option value="6">- Bags</option>
                                        <option value="7">- Accessories</option>
                                    </optgroup>
                                </select>
                            </div>
                            <div class="input-box">
                                <button type="submit" title="Search" class="action search"><i class="icon an an-search-l"></i></button>
                                <input type="text" name="q" value="" placeholder="{% if searchPlaceholder %}{{ searchPlaceholder }}{% else %}Search by keyword or #{% endif %}" class="input-text">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        {% endif %}
        <!--End Search Popup-->
    </header>
    <!--End Header-->
    <!--Main Navigation Desktop-->
    {% if showMenu %}
    <div class="menu-outer d-none d-lg-block">
        <div class="container-fluid">
            <div class="row">
                <div class="col-1 col-sm-1 col-md-1 col-lg-12 align-self-center d-menu-col">
                    <!-- 桌面导航菜单 - 通过HTMX加载 -->
                    <div id="desktop-navigation" hx-get="/home/<USER>" hx-trigger="load" hx-select="nav#AccessibleNav">
                        <!-- 导航菜单将通过HTMX加载 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    <!--End Main Navigation Desktop-->
</div>
<!--End Header wrap-->
<!--Mobile Menu - 通过HTMX加载-->
{% if showMenu %}
<div id="mobile-navigation" hx-get="/home/<USER>" hx-trigger="load" hx-select=".mobile-nav-wrapper">
    <!-- 移动端导航菜单将通过HTMX加载 -->
</div>
{% endif %}
<!--End Mobile Menu-->
{% endif %}
<!--MiniCart Drawer-->
<div class="minicart-right-drawer modal right fade" id="minicart-drawer">
    <div class="modal-dialog">
        <div class="modal-content">
            <div id="cart-drawer" class="block block-cart">
                <div class="minicart-header">
                    <a href="javascript:void(0);" class="close-cart" data-bs-dismiss="modal" aria-label="Close"><i class="an an-times-r" aria-hidden="true" data-bs-toggle="tooltip" data-bs-placement="left" title="Close"></i></a>
                    <h4 class="fs-6 text-black">Your cart (2 Items)</h4>
                </div>
                <div class="minicart-content">
                    <ul class="m-0 clearfix">
                        <li class="item d-flex justify-content-center align-items-center">
                            <a class="product-image" href="product-layout1.html">
                                <img class="blur-up lazyload" src="{{static_path}}/assets/images/products/cart-product-img1.jpg" data-src="{{static_path}}/assets/images/products/cart-product-img1.jpg" alt="image" title="">
                            </a>
                            <div class="product-details">
                                <a class="product-title" href="product-layout1.html">Tops Bras Gym</a>
                                <div class="variant-cart">Black / XL</div>
                                <div class="priceRow">
                                    <div class="product-price">
                                        <span class="money">$59.00</span>
                                    </div>
                                </div>
                            </div>
                            <div class="qtyDetail text-center">
                                <div class="wrapQtyBtn">
                                    <div class="qtyField">
                                        <a class="qtyBtn minus" href="javascript:void(0);"><i class="icon an an-minus-r" aria-hidden="true"></i></a>
                                        <input type="text" name="quantity" value="1" class="qty rounded-0">
                                        <a class="qtyBtn plus" href="javascript:void(0);"><i class="icon an an-plus-l" aria-hidden="true"></i></a>
                                    </div>
                                </div>
                                <a href="#" class="edit-i remove"><i class="icon an an-edit-l" data-bs-toggle="tooltip" data-bs-placement="top" title="Edit"></i></a>
                                <a href="#" class="remove"><i class="an an-times-r" data-bs-toggle="tooltip" data-bs-placement="top" title="Remove"></i></a>
                            </div>
                        </li>
                        <li class="item d-flex justify-content-center align-items-center">
                            <a class="product-image" href="product-layout1.html">
                                <img class="blur-up lazyload" src="{{static_path}}/assets/images/products/cart-product-img1.jpg" data-src="{{static_path}}/assets/images/products/cart-product-img1.jpg" alt="image" title="">
                            </a>
                            <div class="product-details">
                                <a class="product-title" href="product-layout1.html">Fitness Set Tool</a>
                                <div class="variant-cart">Blue / XL</div>
                                <div class="priceRow">
                                    <div class="product-price">
                                        <span class="money">$199.00</span>
                                    </div>
                                </div>
                            </div>
                            <div class="qtyDetail text-center">
                                <div class="wrapQtyBtn">
                                    <div class="qtyField">
                                        <a class="qtyBtn minus" href="javascript:void(0);"><i class="icon an an-minus-r" aria-hidden="true"></i></a>
                                        <input type="text" name="quantity" value="1" class="qty rounded-0">
                                        <a class="qtyBtn plus" href="javascript:void(0);"><i class="icon an an-plus-l" aria-hidden="true"></i></a>
                                    </div>
                                </div>
                                <a href="#" class="edit-i remove"><i class="icon an an-edit-l" data-bs-toggle="tooltip" data-bs-placement="top" title="Edit"></i></a>
                                <a href="#" class="remove"><i class="an an-times-r" data-bs-toggle="tooltip" data-bs-placement="top" title="Remove"></i></a>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="minicart-bottom text-black">
                    <div class="shipinfo text-center mb-3 text-uppercase">
                        <p class="freeShipMsg"><i class="an an-truck fs-5 me-2 align-middle"></i>SPENT <b>$199.00</b> MORE FOR FREE SHIPPING</p>
                    </div>
                    <div class="subtotal">
                        <span>Total:</span>
                        <span class="product-price">$93.13</span>
                    </div>
                    <a href="/Order/PaymentInfo" class="w-100 btn btn-outline-primary p-2 my-2 rounded-0 proceed-to-checkout">Proceed to Checkout</a>
                    <a href="/Order/Cart" class="w-100 btn rounded-0 cart-btn">View Cart</a>
                </div>
            </div>
        </div>
    </div>
</div>
<!--End MiniCart Drawer-->
<div class="modalOverly"></div>

<!-- 动态Logo样式 -->
{% if logoConfig and logoConfig.MobileLogoWidth %}
<style>
    @media (max-width: 991px) {
        .logo-img {
            width: {{ logoConfig.MobileLogoWidth }} !important;
        }
    }
</style>
{% endif %}

<!-- 获取用户配置脚本 -->
<script>
    $(function () {
        //获取用户配置
        GetUserConf();
    })

    //获取用户配置
    function GetUserConf() {
        $.ajax({
            url: '/Account/GetUserConf',
            method: 'GET',
            contentType: 'application/json',
            success: function (data) {
                $(".wishlist-count").text(data.wishlistCount);
                $(".site-cart-count").text(data.cartCount);
            },
            error: function (xhr, status, error) {
                console.log('获取用户配置失败:', error);
            },
            complete: function () {
                // 可以在这里添加完成后的处理逻辑
            }
        });
    }
</script>

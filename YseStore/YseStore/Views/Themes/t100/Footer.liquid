<!-- 解析ViewData["VisualPageData"]中的footer配置 -->
{% assign visualPageData = ViewData["VisualPageData"] | jsonparse %}
{% assign footerConfig = null %}
{% assign showFooter = true %}
{% assign showCopyright = true %}
{% assign showPaymentIcons = true %}
{% assign newsletterConfig = null %}
{% assign socialConfig = null %}
{% assign quickMenus = null %}

{% comment %} 从Plugins列表中筛选footer类型的插件 {% endcomment %}
{% assign footerPlugin = null %}
{% for plugin in visualPageData.Plugins %}
    {% assign cleanType = plugin.Type | strip %}
    {% if cleanType == "footer" %}
        {% assign footerPlugin = plugin %}
        {% break %}
    {% endif %}
{% endfor %}
{% if footerPlugin %}
    {% assign footerConfig = footerPlugin %}

    <!-- 解析Settings -->
    {% if footerConfig.Settings %}
        {% assign copyrightValue = footerConfig.Settings.Copyright | append: "" %}
        {% if copyrightValue == "0" %}
            {% assign showCopyright = false %}
        {% endif %}

        {% assign paymentValue = footerConfig.Settings.PaymentMethodIcons | append: "" %}
        {% if paymentValue == "0" %}
            {% assign showPaymentIcons = false %}
        {% endif %}
    {% endif %}

    <!-- 解析Blocks -->
    {% if footerConfig.Blocks %}
        {% assign newsletterConfig = footerConfig.Blocks.Newsletter-1 %}
        {% assign socialConfig = footerConfig.Blocks.Social-1 %}

        <!-- 收集QuickMenu配置 -->
        {% assign quickMenus = "" | split: "," %}
        {% for block in footerConfig.Blocks %}
            {% if block[0] contains "QuickMenu-" %}
                {% assign quickMenus = quickMenus | concat: block[1] %}
            {% endif %}
        {% endfor %}
    {% endif %}
{% endif %}

{% if showFooter %}
<section class="store-features">
    <div class="container">
        <div class="row store-info">
            <div class="col-12 col-sm-6 col-md-6 col-lg-3 mb-3 my-lg-0">
                <a class="d-flex clr-none" href="#">
                    <i class="an an-truck-l"></i>
                    <div class="detail">
                        <h5 class="h4 mb-1 body-font">FREE SHIPPING &amp; RETURN</h5>
                        <p class="sub-text">Free shipping on all US orders</p>
                    </div>
                </a>
            </div>
            <div class="col-12 col-sm-6 col-md-6 col-lg-3 mb-3 my-lg-0">
                <a class="d-flex clr-none" href="#">
                    <i class="an an-dollar-sign-l"></i>
                    <div class="detail">
                        <h5 class="h4 mb-1 body-font">MONEY GUARANTEE</h5>
                        <p class="sub-text">30 days guarantee</p>
                    </div>
                </a>
            </div>
            <div class="col-12 col-sm-6 col-md-6 col-lg-3 mb-3 mb-sm-0 my-lg-0">
                <a class="d-flex clr-none" href="#">
                    <i class="an an-credit-card-l"></i>
                    <div class="detail">
                        <h5 class="h4 mb-1 body-font">SECURE PAYMENT</h5>
                        <p class="sub-text">Secured &amp; trusted payment</p>
                    </div>
                </a>
            </div>
            <div class="col-12 col-sm-6 col-md-6 col-lg-3 mb-0 mb-sm-0 my-lg-0">
                <a class="d-flex clr-none" href="#">
                    <i class="an an-truck-l"></i>
                    <div class="detail">
                        <h5 class="h4 mb-1 body-font">Wide choice</h5>
                        <p class="sub-text">1000+ items available</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</section>
<!--Footer-->
<div class="footer">
    <div class="footer-above clearfix">
        <div class="container">
            <div class="row">
                <div class="col-12 footer-newsletter">
                    {% comment %} Newsletter订阅区域 - 根据配置显示 {% endcomment %}
                    {% if newsletterConfig %}
                        <h2 class="h1 text-white ff-light text-center mb-2">
                            {% if newsletterConfig.Title %}{{ newsletterConfig.Title }}{% else %}Subscribe now to get a gift with your first order!{% endif %}
                        </h2>
                        <p class="display-6 text-white ff-bold text-center mb-2">
                            {% if newsletterConfig.SubTitle %}{{ newsletterConfig.SubTitle }}{% else %}Get an Exclusive 12% Off Your First Purchase{% endif %}
                        </p>
                    {% else %}
                        <h2 class="h1 text-white ff-light text-center mb-2">Subscribe now to get a gift with your first order!</h2>
                        <p class="display-6 text-white ff-bold text-center mb-2">Get an Exclusive 12% Off Your First Purchase</p>
                    {% endif %}
                    <div class="row justify-content-md-center">
                        <div class="col-12 col-sm-12 col-md-6">
                            <div class="ft-newsletter mb-4">
                                <div class="display-table pe-lg-3">
                                    <div class="display-table-cell">
                                        <form hx-post="/account/OnSubscribute" method="post"
                                              hx-target="#subscribe-result"
                                              hx-swap="afterbegin"
                                              hx-on::after-request="if(event.detail.successful) this.reset()"
                                              id="SubscribeForm" class="SubscribeForm">
                                            <div class="input-group">
                                                <input type="email" class="border-0 input-group__field newsletter-input txtSubscribeEmail" name="email" value=""
                                                       placeholder="{% if newsletterConfig and newsletterConfig.InputPlaceholder %}{{ newsletterConfig.InputPlaceholder }}{% else %}Your Email Address{% endif %}"
                                                       onkeyup="this.setCustomValidity('')"
                                                       hx-on:htmx:validation:validate="if(this.value == '') {
							                                this.setCustomValidity('Please enter Your Email')
							                                $(this).addClass('is-invalid')
						                                }">
                                                <span class="input-group__btn">
                                                    <button type="submit" class="btn btn-primary newsletter__submit rounded-end" name="commit"
                                                            hx-on="htmx:configRequest: this.disabled = true; this.innerText = 'Submitting...'"
                                                            hx-disabled-elt="this"
                                                            hx-indicator="#subscribe-spinner">
                                                        {% if newsletterConfig and newsletterConfig.ButtonText %}{{ newsletterConfig.ButtonText }}{% else %}Subscribe{% endif %}
                                                        <span id="subscribe-spinner" class="htmx-indicator">
                                                            <i class="icon an an-spinner an-spinner-l"></i>
                                                        </span>
                                                    </button>
                                                </span>
                                            </div>
                                        </form>
                                        <div id="subscribe-result"></div>
                                    </div>
                                </div>
                                <div class="privacy">
                                    <ul class="privacy-item ">
                                        <li>By clicking the SUBSCRIBE button, you are agreeing to our <a class="text-decoration-underline" href="/page/terms-and-conditions" target="_blank">Terms of Use</a> and <a class="text-decoration-underline" href="/page/privacy-policy" target="_blank">Privacy & Cookie Policy</a></li>
                                        <li>If you want to unsubsribe the marketing email, Please contact via email <a href="mailto:<EMAIL>"><EMAIL></a> <i class="an an-question-cil hintmessage" data-cart-render="item_count" data-bs-html="true" data-bs-toggle="tooltip" data-bs-placement="bottom" data-bs-original-title="Verify your identity by email."></i>.</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="footer-top clearfix pb-5">
        <div class="container">
            
            <div class="row">
                <div class="col-12 col-sm-12 col-md-6 col-lg-3 footer-links">
                    <h4 class="h4">Contact Us</h4>
                    <p class="phone d-flex align-items-center"><i class="an an-phone-rc"></i><b class="me-1">Phone:</b> (+86) 155 1710 3523</p>
                    <p class="whatsapp d-flex align-items-center"><i class="an an-whatsapp"></i><b class="me-1">WhatsApp:</b> (+86) 150 3808 6746</p>
                    <p class="email d-flex align-items-center"><i class="an an-envelope-l"></i><b class="me-1">Email:</b> <EMAIL></p>
                </div>
                {% comment %} 动态底部导航菜单 - 通过HTMX加载 {% endcomment %}
                <div hx-get="/home/<USER>" hx-trigger="load" hx-swap="outerHTML">
                    <!-- 底部导航菜单将通过HTMX加载 -->
                </div>
            </div>
        </div>
    </div>
    <div class="footer-bottom clearfix pb-5">
        <div class="container">
            <div class="text-md-end text-center footer-contact worldwide"><a href="#siteModal" class="siteModal">Worldwide <i class="an an-map-marker-al"></i></a></div>
            <hr class="mb-3">
            <div class="row">
                <div class="col-12 col-md-9 col-lg-10 copytext-col">
                    {% comment %} 版权信息 - 根据配置显示 {% endcomment %}
                    {% if showCopyright %}
                    <div class="copytext text-md-start text-center">© 2024 RETEVIS, Inc. All rights reserved.</div>
                    <ul class="copyright-links text-md-start text-center">
                        <li class="start"><a href="/page/terms-and-conditions" target="_blank">Terms and Conditions</a></li>
                        <li><a href="/page/privacy-policy" target="_blank">Privacy Policy</a></li>
                        <li><a href="/page/returns-policy" target="_blank">Returns Policy</a></li>
                        <li><a href="/page/warranty" target="_blank">Warranty Policy</a></li>
                        <li><a href="/page/free-shipping" target="_blank">Shipping Policy</a></li>
                        <li class="end"><a href="/page/copyright-noticeam" target="_blank">IP Notice</a></li>
                    </ul>
                    {% endif %}
                </div>
                <div class="col-12 col-md-3 col-lg-2">
                    {% comment %} 支付方式图标 - 根据配置显示 {% endcomment %}
                    {% if showPaymentIcons %}
                    <div class="protection text-md-end text-center">
                        <img src="{{static_path}}/assets/images/footer/dmca.webp" alt="This site is protected by Trustwave's Trusted Commerce program"><img src="{{static_path}}/assets/images/footer/trustwave.webp" alt="DMCA.com Protection Status">
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
<!--End Footer-->

<!-- Newsletter订阅回调脚本 -->
<script>
    // Newsletter订阅成功回调处理
    document.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.elt.id === 'SubscribeForm' && event.detail.successful) {
            try {
                const response = JSON.parse(event.detail.xhr.responseText);
                const resultDiv = document.getElementById('subscribe-result');
                if (resultDiv) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-${response.status ? 'success' : 'danger'} mt-2">
                            ${response.msg}
                        </div>`;

                    // 3秒后自动隐藏
                    setTimeout(() => {
                        resultDiv.innerHTML = '';
                    }, 10000);
                }
            } catch (e) {
                console.log('Newsletter subscription response parsing error:', e);
            }
        }
    });
</script>



using Entitys;
using Newtonsoft.Json.Linq;


namespace YseStore.IService.Visual
{
    public interface IVisualPluginsService : IBaseServices<visual_plugins>
    {

        /// <summary>
        /// 读取插件JSON配置
        /// </summary>
        /// <param name="type"></param>
        /// <param name="mode"></param>
        /// <param name="drafts"></param>
        /// <returns></returns>
        Task<JObject> GetPluginsConfig(string type, string mode, visual_drafts drafts);

        /// <summary>
        /// 根据插件ID列表批量获取插件
        /// </summary>
        /// <param name="pluginIds">插件ID列表</param>
        /// <returns></returns>
        Task<List<visual_plugins>> GetPluginsByIdsAsync(List<int> pluginIds);

        /// <summary>
        /// 生成html
        /// </summary>
        /// <param name="row"></param>
        /// <returns></returns>
        Task<string> MenuHtml(visual_plugins row);


        /// <summary>
        /// 获取可视化模块类型数据
        /// </summary>
        /// <param name="draftsid"></param>
        /// <param name="page"></param>
        /// <param name="type"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        Task<List<Dictionary<string, object>>> GetModeData(int draftsid, string page, string type, string keyword);

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        Task<JObject> GetMapConfig();
    }
}

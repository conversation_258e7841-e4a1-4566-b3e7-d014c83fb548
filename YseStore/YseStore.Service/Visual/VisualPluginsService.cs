using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common;
using YseStore.Common.Cache;
using YseStore.Common.Const;
using YseStore.IService.Visual;

namespace YseStore.Service.Visual
{
    /// <summary>
    /// 店铺装修插件
    /// </summary>
    public class VisualPluginsService : BaseServices<visual_plugins>, IVisualPluginsService
    {
        private readonly ISqlSugarClient db;
        private readonly ICaching _caching;
        private readonly ILogger<VisualPluginsService> _logger;
        private readonly IStringLocalizer<VisualPluginsService> T;

        public VisualPluginsService(ISqlSugarClient db, ICaching caching, ILogger<VisualPluginsService> logger, IStringLocalizer<VisualPluginsService> localizer)
        {
            this.db = db;
            _caching = caching;
            _logger = logger;
            T = localizer;
        }


        /// <summary>
        /// 读取插件JSON配置
        /// </summary>
        /// <param name="type">插件类型</param>
        /// <param name="mode"></param>
        /// <param name="drafts">插件风格</param>
        /// <returns></returns>
        public async Task<JObject> GetPluginsConfig(string type, string mode, visual_drafts drafts)
        {

            string config = await System.IO.File.ReadAllTextAsync(System.IO.Path.Combine(System.Environment.CurrentDirectory, $"wwwroot/template/default/cusvis_mode/{type}/{mode}/config.json"));
            if (!string.IsNullOrEmpty(config))
            {
                //配置
                var configModel = config.JsonToObj<JObject>();

                //themesConfig
                var themesConfig = await System.IO.File.ReadAllTextAsync(System.IO.Path.Combine(System.Environment.CurrentDirectory, $"wwwroot/template/default/inc/themes.json"));
                if (!themesConfig.IsNullOrEmpty())
                {
                    var themesConfigModel = themesConfig.JsonToObj<JObject>();

                    var pluginsExtConfig = themesConfigModel["PluginsExtConfig"][$"{type}-{mode}"];
                    if (pluginsExtConfig != null && !pluginsExtConfig.IsNullOrEmpty())
                    {
                        var obj = pluginsExtConfig.ToObject<JObject>();

                        configModel = ArrayMerge(configModel, obj);
                    }

                }


                return configModel;

            }

            return null;

        }

        /// <summary>
        /// 递归合并数组，并排除空值
        /// </summary>
        /// <param name="arrs"></param>
        /// <returns></returns>
        public static JObject ArrayMerge(params JObject[] jObjects)
        {
            JObject merged = new JObject();

            foreach (var jObject in jObjects)
            {
                if (jObject == null) continue;

                foreach (var property in jObject)
                {
                    var key = property.Key;
                    var value = property.Value;

                    if (value.Type == JTokenType.Object && merged[key] is JObject mergedObj)
                    {
                        // Recursively merge nested JObject
                        merged[key] = ArrayMerge(mergedObj, (JObject)value);
                    }
                    else
                    {
                        // Directly set or override the value
                        merged[key] = value;
                    }
                }
            }

            return merged;
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        public async Task<string> MenuHtml(visual_plugins row)
        {
            try
            {
                var config = row.Config.JsonToObj<JObject>();
                var display = config != null && config["Display"]?.ToString() == "0" ? false : true;

                var name = T[$"type_menu.{row.Type}"].Value;
                string mode = row.Mode.Replace("mode_", "");
                string html = $"<div class=\"menu_item {(display ? "" : "item_close")} \" data-fixed-plugins=\"{row.PId}\">";

                if (!new[] { "header", "footer" }.Contains(row.Type))
                {
                    html += "<div class=\"item_move\"></div>";
                }

                html += $"<div class=\"item_icon icon_{row.Type}\"></div>";
                html += $"<div class=\"item_name\" data-name=\"{name}\">{name}</div>";

                if (!new[] { "header", "footer" }.Contains(row.Type))
                {
                    html += $"<div class=\"item_mode\">{name}-{mode}</div>";
                }

                var specialTypes = new[] {"header", "banner", "footer", "product_list", "product_purchase",
                             "combination_purchase", "product_description", "article",
                             "blog_list", "blog_detail"};

                if (!specialTypes.Contains(row.Type))
                {
                    html += "<div class=\"item_display\"></div>";
                }
                else if (new[] { "article", "product_description" }.Contains(row.Type))
                {
                    html += "<div class=\"item_display\"></div>";
                }

                html += "<div class=\"clear\"></div>";
                html += $"<input type=\"hidden\" name=\"PId[]\" value=\"{row.PId}\" />";
                html += $"<input type=\"hidden\" name=\"visual[PId-{row.PId}][Config][Display]\" value=\"{display.ToString().ToLower()}\" />";
                html += "</div>";

                return html;


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);

                return "";
            }


        }


        /// <summary>
        /// 根据插件ID列表批量获取插件
        /// </summary>
        /// <param name="pluginIds">插件ID列表</param>
        /// <returns></returns>
        public async Task<List<visual_plugins>> GetPluginsByIdsAsync(List<int> pluginIds)
        {
            try
            {
                if (pluginIds == null || !pluginIds.Any())
                {
                    return new List<visual_plugins>();
                }

                return await db.Queryable<visual_plugins>()
                    .Where(it => pluginIds.Contains(it.PId))
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据插件ID列表批量获取插件失败，PluginIds: {PluginIds}", string.Join(",", pluginIds ?? new List<int>()));
                return new List<visual_plugins>();
            }
        }


        /// <summary>
        /// 获取可视化模块类型数据
        /// </summary>
        /// <param name="draftsid"></param>
        /// <param name="page"></param>
        /// <param name="type"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public async Task<List<Dictionary<string, object>>> GetModeData(int draftsid, string page, string type, string keyword)
        {
            //var c = AppConfig.Params; // 相当于 Yii::$app->params
            var result = new List<Dictionary<string, object>>();
            string typeKey = string.Empty;
            string numberKey = string.Empty;

            // 关键词搜索处理
            if (!string.IsNullOrEmpty(keyword))
            {
                var typeMenuDict = GlobalLangVars.ViewTypeMenu; // 假设返回 Dictionary<string, string>
                if (keyword.Contains("-"))
                {
                    var keywordAry = keyword.Split("-");
                    typeKey = keywordAry.ElementAtOrDefault(0) ?? "";
                    numberKey = "mode_" + (int.TryParse(keywordAry.ElementAtOrDefault(1), out int num) ? num : 0);
                }
                else if (int.TryParse(keyword, out int num))
                {
                    numberKey = "mode_" + num;
                }
                else
                {
                    typeKey = keyword;
                }

                if (!string.IsNullOrEmpty(typeKey) && type == "all")
                {
                    type = "";
                }

                if (!string.IsNullOrEmpty(typeKey))
                {
                    var searchResult = typeMenuDict.FirstOrDefault(x => x.Value == typeKey).Key;
                    if (searchResult != null)
                    {
                        type = searchResult;
                    }
                    else
                    {
                        foreach (var kvp in typeMenuDict)
                        {
                            if (kvp.Value.Contains(typeKey))
                            {
                                type = kvp.Key;
                                break;
                            }
                        }
                    }
                }
            }

            var Drafts = await db.Queryable<visual_drafts>().Where(it => it.DraftsId == draftsid).FirstAsync();

            // 查找插件内容
            var map = await GetMapConfig(); // Dictionary<string, List<string>>
            var mapAry = type == "all" || string.IsNullOrEmpty(type)
                ? map
                : new JObject { [type] = map.ContainsKey(type) ? map[type] : new JObject() };

            int keyIndex = 0;
            foreach (var kv in mapAry)
            {
                string k = kv.Key;
                foreach (var v2 in kv.Value.ToObject<JObject>().Properties())
                {
                    if (!string.IsNullOrEmpty(numberKey) && numberKey != v2.Name) continue;

                    if (new[] { "header", "banner", "footer", "product_list", "product_purchase", "combination_purchase", "product_description", "article", "download" }.Contains(k))
                        continue;

                    var pluginsConfig =await GetPluginsConfig(k, v2.Name, Drafts);
                    if (pluginsConfig == null) continue;

                    int isInPages = 0;
                    var pagesLimitAry = pluginsConfig["Config"]?["PagePermit"]?.ToObject<List<string>>() ?? new List<string>();
                    if (pagesLimitAry.Contains(page) || (pagesLimitAry.FirstOrDefault() == "*"))
                        isInPages = 1;

                    //var manageRules = AppConfig.Params["manage"]["rules"].ToObject<List<string>>();
                    //if ((k == "blog" && !manageRules.Contains("blogApp")) || (k == "gallery" && !manageRules.Contains("galleryApp")))
                       // isInPages = 0;

                    var item = new Dictionary<string, object>
            {
                { "Type", k },
                { "Mode", v2 },
                { "IsInPages", isInPages }
            };

                    if (pluginsConfig["Config"]?["PluginDescription"] != null)
                    {
                        item["PluginDescription"] = pluginsConfig["Config"]["PluginDescription"].ToString();
                    }

                    result.Add(item);
                    keyIndex++;
                }
            }

            return result;
        }



        public async Task<JObject> GetMapConfig()
        {
            //var c = AppConfig.Params;
            //var rootPath = c["root_path"]?.ToString() ?? "";
            //var mapPath = c["manage"]?["cusvis_path_v2"]?.ToString() ?? "";
            var configPath = await System.IO.File.ReadAllTextAsync(System.IO.Path.Combine(System.Environment.CurrentDirectory, $"wwwroot/template/default/cusvis_mode/config.json"));

            if (!System.IO.File.Exists(configPath))
                return new JObject();

            string jsonContent = System.IO.File.ReadAllText(configPath);
            var config = jsonContent.JsonToObj<JObject>();

            return config ?? new JObject();
        }




    }
}
